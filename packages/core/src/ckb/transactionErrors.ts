import { fixedPointToString } from "../fixedPoint/index.js";
import { Num, numFrom, NumLike } from "../num/index.js";

export class ErrorTransactionInsufficientCapacity extends Error {
  public readonly amount?: Num;

  constructor(amountLike: NumLike) {
    const amount = numFrom(amountLike);
    super(`Insufficient CKB, need ${fixedPointToString(amount)} extra CKB`);
    this.amount = amount;
  }
}

export class ErrorTransactionInsufficientCoin extends Error {
  public readonly amount?: Num;

  constructor(amountLike: NumLike) {
    const amount = numFrom(amountLike);
    super(`Insufficient coin, need ${amount} extra coin`);
    this.amount = amount;
  }
}

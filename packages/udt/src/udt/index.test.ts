import { ccc } from "@ckb-ccc/core";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { Udt } from "./index.js";

let client: ccc.Client;
let signer: ccc.Signer;
let lock: ccc.Script;
let type: ccc.Script;
let udt: Udt;

beforeEach(async () => {
  client = new ccc.ClientPublicTestnet();
  signer = new ccc.SignerCkbPublicKey(
    client,
    "0x026f3255791f578cc5e38783b6f2d87d4709697b797def6bf7b3b9af4120e2bfd9",
  );
  lock = (await signer.getRecommendedAddressObj()).script;

  type = await ccc.Script.fromKnownScript(
    client,
    ccc.KnownScript.XUdt,
    "0xf8f94a13dfe1b87c10312fb9678ab5276eefbe1e0b2c62b4841b1f393494eff2",
  );

  // Create UDT instance
  udt = new Udt(
    {
      txHash:
        "0x4e2e832e0b1e7b5994681b621b00c1e65f577ee4b440ef95fa07db9bb3d50269",
      index: 0,
    },
    type,
  );
});

describe("Udt", () => {
  describe("completeInputs", () => {
    // Mock cells with 100 UDT each (10 cells total = 1000 UDT)
    let mockUdtCells: ccc.Cell[];

    beforeEach(async () => {
      // Create mock cells after type is initialized
      mockUdtCells = Array.from({ length: 10 }, (_, i) =>
        ccc.Cell.from({
          outPoint: {
            txHash: `0x${"0".repeat(63)}${i.toString(16)}`,
            index: 0,
          },
          cellOutput: {
            capacity: ccc.fixedPointFrom(142),
            lock,
            type,
          },
          outputData: ccc.numLeToBytes(100, 16), // 100 UDT tokens
        }),
      );
    });

    beforeEach(() => {
      // Mock the findCells method to return our mock UDT cells
      vi.spyOn(signer, "findCells").mockImplementation(
        async function* (filter) {
          if (filter.script && ccc.Script.from(filter.script).eq(type)) {
            for (const cell of mockUdtCells) {
              yield cell;
            }
          }
        },
      );

      // Mock client.getCell to return the cell data for inputs
      vi.spyOn(client, "getCell").mockImplementation(async (outPoint) => {
        const cell = mockUdtCells.find((c) => c.outPoint.eq(outPoint));
        return cell;
      });
    });

    it("should return 0 when no UDT balance is needed", async () => {
      const tx = ccc.Transaction.from({
        outputs: [],
      });

      const addedCount = await udt.completeInputs(tx, signer);
      expect(addedCount).toBe(0);
    });

    it("should collect exactly the required UDT balance", async () => {
      const tx = ccc.Transaction.from({
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(150, 16)], // Need 150 UDT
      });

      const addedCount = await udt.completeInputs(tx, signer);

      // Should add 2 cells (200 UDT total) to have at least 2 inputs
      expect(addedCount).toBe(2);
      expect(tx.inputs.length).toBe(2);

      // Verify the inputs are UDT cells
      const inputBalance = await udt.getInputsBalance(tx, client);
      expect(inputBalance).toBe(ccc.numFrom(200));
    });

    it("should collect exactly one cell when amount matches exactly", async () => {
      const tx = ccc.Transaction.from({
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(100, 16)], // Need exactly 100 UDT
      });

      const addedCount = await udt.completeInputs(tx, signer);

      // Should add only 1 cell since it matches exactly
      expect(addedCount).toBe(1);
      expect(tx.inputs.length).toBe(1);

      const inputBalance = await udt.getInputsBalance(tx, client);
      expect(inputBalance).toBe(ccc.numFrom(100));
    });

    it("should handle balanceTweak parameter", async () => {
      const tx = ccc.Transaction.from({
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(100, 16)], // Need 100 UDT
      });

      // Add 50 extra UDT requirement via balanceTweak
      const addedCount = await udt.completeInputs(tx, signer, 50);

      // Should add 2 cells to cover 150 UDT total requirement
      expect(addedCount).toBe(2);
      expect(tx.inputs.length).toBe(2);

      const inputBalance = await udt.getInputsBalance(tx, client);
      expect(inputBalance).toBe(ccc.numFrom(200));
    });

    it("should return 0 when existing inputs already satisfy the requirement", async () => {
      const tx = ccc.Transaction.from({
        inputs: [
          {
            previousOutput: mockUdtCells[0].outPoint,
          },
          {
            previousOutput: mockUdtCells[1].outPoint,
          },
        ],
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(150, 16)], // Need 150 UDT, already have 200
      });

      const addedCount = await udt.completeInputs(tx, signer);

      // Should not add any inputs since we already have enough
      expect(addedCount).toBe(0);
      expect(tx.inputs.length).toBe(2);
    });

    it("should throw error when insufficient UDT balance available", async () => {
      const tx = ccc.Transaction.from({
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(1500, 16)], // Need 1500 UDT, only have 1000 available
      });

      await expect(udt.completeInputs(tx, signer)).rejects.toThrow(
        "Insufficient coin, need 500 extra coin",
      );
    });

    it("should handle multiple UDT outputs correctly", async () => {
      const tx = ccc.Transaction.from({
        outputs: [
          {
            lock,
            type,
          },
          {
            lock,
            type,
          },
        ],
        outputsData: [
          ccc.numLeToBytes(100, 16), // First output: 100 UDT
          ccc.numLeToBytes(150, 16), // Second output: 150 UDT
        ], // Total: 250 UDT needed
      });

      const addedCount = await udt.completeInputs(tx, signer);

      // Should add 3 cells to cover 250 UDT requirement (300 UDT total)
      expect(addedCount).toBe(3);
      expect(tx.inputs.length).toBe(3);

      const inputBalance = await udt.getInputsBalance(tx, client);
      expect(inputBalance).toBe(ccc.numFrom(300));

      const outputBalance = udt.getOutputsBalance(tx);
      expect(outputBalance).toBe(ccc.numFrom(250));
    });

    it("should skip cells that are already used as inputs", async () => {
      // Pre-add one of the mock cells as input
      const tx = ccc.Transaction.from({
        inputs: [
          {
            previousOutput: mockUdtCells[0].outPoint,
          },
        ],
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(150, 16)], // Need 150 UDT, already have 100
      });

      const addedCount = await udt.completeInputs(tx, signer);

      // Should add 1 more cell (since we already have 1 input with 100 UDT)
      expect(addedCount).toBe(1);
      expect(tx.inputs.length).toBe(2);

      const inputBalance = await udt.getInputsBalance(tx, client);
      expect(inputBalance).toBe(ccc.numFrom(200));
    });

    it("should add one cell when user needs less than one cell", async () => {
      const tx = ccc.Transaction.from({
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(50, 16)], // Need only 50 UDT (less than one cell)
      });

      const addedCount = await udt.completeInputs(tx, signer);

      // UDT completeInputs adds minimum inputs needed
      expect(addedCount).toBe(1);
      expect(tx.inputs.length).toBe(1);

      const inputBalance = await udt.getInputsBalance(tx, client);
      expect(inputBalance).toBe(ccc.numFrom(100));
    });

    it("should use only one cell when user has only one cell available", async () => {
      // Mock signer to return only one cell
      vi.spyOn(signer, "findCells").mockImplementation(
        async function* (filter) {
          if (filter.script && ccc.Script.from(filter.script).eq(type)) {
            yield mockUdtCells[0]; // Only yield the first cell
          }
        },
      );

      const tx = ccc.Transaction.from({
        outputs: [
          {
            lock,
            type,
          },
        ],
        outputsData: [ccc.numLeToBytes(50, 16)], // Need only 50 UDT
      });

      const addedCount = await udt.completeInputs(tx, signer);

      // Should use only 1 cell since that's all that's available
      expect(addedCount).toBe(1);
      expect(tx.inputs.length).toBe(1);

      const inputBalance = await udt.getInputsBalance(tx, client);
      expect(inputBalance).toBe(ccc.numFrom(100));
    });
  });

  describe("getInputsBalance", () => {
    it("should calculate total UDT balance from inputs", async () => {
      const mockCells = [
        ccc.Cell.from({
          outPoint: { txHash: "0x" + "0".repeat(64), index: 0 },
          cellOutput: { capacity: ccc.fixedPointFrom(142), lock, type },
          outputData: ccc.numLeToBytes(100, 16), // 100 UDT
        }),
        ccc.Cell.from({
          outPoint: { txHash: "0x" + "1".repeat(64), index: 0 },
          cellOutput: { capacity: ccc.fixedPointFrom(142), lock, type },
          outputData: ccc.numLeToBytes(200, 16), // 200 UDT
        }),
      ];

      vi.spyOn(client, "getCell").mockImplementation(async (outPoint) => {
        return mockCells.find((c) => c.outPoint.eq(outPoint));
      });

      const tx = ccc.Transaction.from({
        inputs: [
          { previousOutput: mockCells[0].outPoint },
          { previousOutput: mockCells[1].outPoint },
        ],
      });

      const balance = await udt.getInputsBalance(tx, client);
      expect(balance).toBe(ccc.numFrom(300)); // 100 + 200
    });

    it("should ignore inputs without matching type script", async () => {
      const mockCells = [
        ccc.Cell.from({
          outPoint: { txHash: "0x" + "0".repeat(64), index: 0 },
          cellOutput: { capacity: ccc.fixedPointFrom(142), lock, type },
          outputData: ccc.numLeToBytes(100, 16), // 100 UDT
        }),
        ccc.Cell.from({
          outPoint: { txHash: "0x" + "1".repeat(64), index: 0 },
          cellOutput: { capacity: ccc.fixedPointFrom(142), lock }, // No type script
          outputData: "0x",
        }),
      ];

      vi.spyOn(client, "getCell").mockImplementation(async (outPoint) => {
        return mockCells.find((c) => c.outPoint.eq(outPoint));
      });

      const tx = ccc.Transaction.from({
        inputs: [
          { previousOutput: mockCells[0].outPoint },
          { previousOutput: mockCells[1].outPoint },
        ],
      });

      const balance = await udt.getInputsBalance(tx, client);
      expect(balance).toBe(ccc.numFrom(100)); // Only the UDT cell
    });
  });

  describe("getOutputsBalance", () => {
    it("should calculate total UDT balance from outputs", async () => {
      const tx = ccc.Transaction.from({
        outputs: [
          { lock, type },
          { lock, type },
          { lock }, // No type script
        ],
        outputsData: [
          ccc.numLeToBytes(100, 16), // 100 UDT
          ccc.numLeToBytes(200, 16), // 200 UDT
          "0x", // Not UDT
        ],
      });

      const balance = udt.getOutputsBalance(tx);
      expect(balance).toBe(ccc.numFrom(300)); // 100 + 200, ignoring non-UDT output
    });

    it("should return 0 when no UDT outputs", async () => {
      const tx = ccc.Transaction.from({
        outputs: [{ lock }], // No type script
        outputsData: ["0x"],
      });

      const balance = udt.getOutputsBalance(tx);
      expect(balance).toBe(ccc.numFrom(0));
    });
  });

  describe("completeChangeToLock", () => {
    let mockUdtCells: ccc.Cell[];

    beforeEach(() => {
      mockUdtCells = Array.from({ length: 5 }, (_, i) =>
        ccc.Cell.from({
          outPoint: {
            txHash: `0x${"0".repeat(63)}${i.toString(16)}`,
            index: 0,
          },
          cellOutput: { capacity: ccc.fixedPointFrom(142), lock, type },
          outputData: ccc.numLeToBytes(100, 16), // 100 UDT each
        }),
      );

      vi.spyOn(signer, "findCells").mockImplementation(
        async function* (filter) {
          if (filter.script && ccc.Script.from(filter.script).eq(type)) {
            for (const cell of mockUdtCells) {
              yield cell;
            }
          }
        },
      );

      vi.spyOn(client, "getCell").mockImplementation(async (outPoint) => {
        return mockUdtCells.find((c) => c.outPoint.eq(outPoint));
      });
    });

    it("should add change output when there's excess UDT balance", async () => {
      const changeLock = ccc.Script.from({
        codeHash: "0x" + "9".repeat(64),
        hashType: "type",
        args: "0x1234",
      });

      const tx = ccc.Transaction.from({
        outputs: [{ lock, type }],
        outputsData: [ccc.numLeToBytes(150, 16)], // Need 150 UDT
      });

      const completedTx = await udt.completeChangeToLock(
        tx,
        signer,
        changeLock,
      );

      // Should have original output + change output
      expect(completedTx.outputs.length).toBe(2);
      expect(completedTx.outputs[1].lock.eq(changeLock)).toBe(true);
      expect(completedTx.outputs[1].type?.eq(type)).toBe(true);

      // Change should be 50 UDT (200 input - 150 output)
      const changeAmount = ccc.udtBalanceFrom(completedTx.outputsData[1]);
      expect(changeAmount).toBe(ccc.numFrom(50));
    });

    it("should not add change when no excess balance", async () => {
      const changeLock = ccc.Script.from({
        codeHash: "0x" + "9".repeat(64),
        hashType: "type",
        args: "0x1234",
      });

      const tx = ccc.Transaction.from({
        outputs: [{ lock, type }],
        outputsData: [ccc.numLeToBytes(200, 16)], // Need exactly 200 UDT
      });

      const completedTx = await udt.completeChangeToLock(
        tx,
        signer,
        changeLock,
      );

      // Should only have original output
      expect(completedTx.outputs.length).toBe(1);
    });
  });

  describe("completeBy", () => {
    it("should use signer's recommended address for change", async () => {
      const mockUdtCells = Array.from({ length: 3 }, (_, i) =>
        ccc.Cell.from({
          outPoint: {
            txHash: `0x${"0".repeat(63)}${i.toString(16)}`,
            index: 0,
          },
          cellOutput: { capacity: ccc.fixedPointFrom(142), lock, type },
          outputData: ccc.numLeToBytes(100, 16),
        }),
      );

      vi.spyOn(signer, "findCells").mockImplementation(
        async function* (filter) {
          if (filter.script && ccc.Script.from(filter.script).eq(type)) {
            for (const cell of mockUdtCells) {
              yield cell;
            }
          }
        },
      );

      vi.spyOn(client, "getCell").mockImplementation(async (outPoint) => {
        return mockUdtCells.find((c) => c.outPoint.eq(outPoint));
      });

      const tx = ccc.Transaction.from({
        outputs: [{ lock, type }],
        outputsData: [ccc.numLeToBytes(150, 16)],
      });

      const completedTx = await udt.completeBy(tx, signer);

      // Should have change output with signer's lock
      expect(completedTx.outputs.length).toBe(2);
      expect(completedTx.outputs[1].lock.eq(lock)).toBe(true); // Same as signer's lock
    });
  });
});

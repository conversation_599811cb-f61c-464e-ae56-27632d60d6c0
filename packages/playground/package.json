{"name": "@ckb-ccc/ccc-playground", "version": "1.0.16", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@ckb-ccc/ccc": "workspace:*", "@ckb-ccc/connector-react": "workspace:*", "@monaco-editor/react": "^4.6.0", "@nervina-labs/dob-render": "^0.2.3", "@shikijs/monaco": "^3.2.1", "axios": "^1.7.7", "bech32": "^2.0.0", "isomorphic-ws": "^5.0.0", "lucide-react": "^0.438.0", "monaco-editor": "^0.51.0", "next": "15.3.3", "prettier": "^3.5.3", "react": "^18", "react-dom": "^18", "shiki": "^3.2.1", "typescript": "^5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/webpack-env": "^1.18.5", "eslint": "^8", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "postcss": "^8", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.12", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.1"}, "packageManager": "pnpm@10.8.1"}